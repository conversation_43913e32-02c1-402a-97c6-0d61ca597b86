defmodule Drops.OperationsTest do
  use Drops.DataCase, async: true

  defmodule TestApp do
    use Drops.Operations
  end

  test "it works without schema" do
    defmodule CreateUser do
      use TestApp, :command

      @impl true
      def perform(params) do
        if params[:name] == nil do
          {:error, "name is required"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} = CreateUser.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}
  end

  test "it works with a schema" do
    defmodule CreateUserWithSchema do
      use TestApp, :command

      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def perform(params) do
        if params[:name] != "<PERSON>" do
          {:error, "name is not expected"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}

    {:error, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: ""})

    assert_errors(["name must be filled"], {:error, result})
    assert params == %{name: ""}
  end

  @tag ecto_schemas: [Test.Ecto.UserSchema]
  test "it works with an Ecto schema" do
    defmodule CreateUserWithEctoSchema do
      use TestApp, type: :command, repo: Drops.TestRepo

      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    {:ok, %{result: result, type: :command, params: params}} =
      CreateUserWithEctoSchema.execute(%{name: "Jane Doe", email: "<EMAIL>"})

    assert result == %{name: "Jane Doe"}
    assert params == %{name: "Jane Doe", email: "<EMAIL>"}
  end

  @tag ecto_schemas: [Test.Ecto.UserSchema]
  test "it works with an Ecto schema and custom validation logic" do
    defmodule CreateUserWithCustomValidation do
      use TestApp, type: :command, repo: Drops.TestRepo

      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end

      def validate(changeset) do
        changeset |> validate_required([:email])
      end
    end

    {:ok, %{result: result, type: :command, params: params}} =
      CreateUserWithCustomValidation.execute(%{
        name: "Jane Doe",
        email: "<EMAIL>"
      })

    assert result == %{name: "Jane Doe"}
    assert params == %{name: "Jane Doe", email: "<EMAIL>"}

    {:error, %{result: changeset, type: :command, params: _params}} =
      CreateUserWithCustomValidation.execute(%{name: "Jane Doe", email: ""})

    # Verify that the changeset has validation errors for email
    assert %Ecto.Changeset{} = changeset
    refute changeset.valid?
    assert changeset.errors[:email]
  end

  @tag ecto_schemas: [Test.Ecto.UserSchema]
  test "it works with an Ecto schema and :accept option" do
    defmodule CreateUserWithCustomValidation do
      use TestApp, type: :command, repo: Drops.TestRepo

      schema(Test.Ecto.UserSchema, accept: [:name])

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name, email: user.email}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    {:ok, %{result: result, type: :command, params: params}} =
      CreateUserWithCustomValidation.execute(%{
        name: "Jane Doe",
        email: "<EMAIL>"
      })

    assert result == %{name: "Jane Doe", email: nil}
    assert params == %{name: "Jane Doe"}
  end

  @tag ecto_schemas: [Test.Ecto.UserWithAddressSchema]
  test "it works with an Ecto schema with embedded fields" do
    defmodule CreateUserWithEmbeddedSchema do
      use TestApp, type: :command, repo: Drops.TestRepo

      schema(Test.Ecto.UserWithAddressSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name, address: user.address}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    # Test with valid embedded data
    valid_params = %{
      name: "John Doe",
      email: "<EMAIL>",
      address: %{
        street: "123 Main St",
        city: "Anytown",
        state: "CA",
        zip_code: "12345",
        country: "USA"
      }
    }

    {:ok, %{result: result, type: :command, params: params}} =
      CreateUserWithEmbeddedSchema.execute(valid_params)

    assert result.name == "John Doe"
    assert result.address.street == "123 Main St"
    assert result.address.city == "Anytown"
    assert params.name == "John Doe"
    assert params.address.street == "123 Main St"

    # Test with extra fields that should be filtered out
    params_with_extra = %{
      name: "Jane Smith",
      email: "<EMAIL>",
      address: %{
        street: "456 Oak Ave",
        city: "Springfield",
        state: "IL",
        zip_code: "62701"
      },
      age: 28,
      extra_field: "this should be ignored"
    }

    {:ok, %{result: result, params: params}} =
      CreateUserWithEmbeddedSchema.execute(params_with_extra)

    assert result.name == "Jane Smith"
    assert result.address.street == "456 Oak Ave"
    assert result.address.city == "Springfield"
    # Extra fields should be filtered out
    refute Map.has_key?(params, :age)
    refute Map.has_key?(params, :extra_field)
  end

  describe ":form commands" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    test "it works with an Ecto schema" do
      defmodule CreateUserWithEctoSchema do
        use TestApp, type: :form, repo: Drops.TestRepo

        schema(Test.Ecto.UserSchema)

        @impl true
        def perform(params) do
          case persist(params) do
            {:ok, user} -> {:ok, %{name: user.name}}
            {:error, changeset} -> {:error, changeset}
          end
        end
      end

      {:ok, %{result: result, type: :form, params: params}} =
        CreateUserWithEctoSchema.execute(%{
          "name" => "Jane Doe",
          "email" => "<EMAIL>"
        })

      assert result == %{name: "Jane Doe"}
      assert params == %{name: "Jane Doe", email: "<EMAIL>"}
    end

    @tag ecto_schemas: [Test.Ecto.UserSchema]
    test "Success struct implements Phoenix.HTML.FormData protocol" do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          defmodule CreateUserFormSuccess do
            use TestApp, type: :form, repo: Drops.TestRepo

            schema(Test.Ecto.UserSchema)

            @impl true
            def perform(params) do
              {:ok, params}
            end
          end

          {:ok, success} =
            CreateUserFormSuccess.execute(%{
              "name" => "Jane Doe",
              "email" => "<EMAIL>"
            })

          # Test that Success struct can be converted to a form
          form = Phoenix.HTML.FormData.to_form(success, [])
          assert is_struct(form)
          assert form.data == %{name: "Jane Doe", email: "<EMAIL>"}

          # Test input_value function
          assert Phoenix.HTML.FormData.input_value(success, form, :name) == "Jane Doe"

          assert Phoenix.HTML.FormData.input_value(success, form, :email) ==
                   "<EMAIL>"

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end

    @tag ecto_schemas: [Test.Ecto.UserSchema]
    test "Failure struct implements Phoenix.HTML.FormData protocol" do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          defmodule CreateUserFormFailure do
            use TestApp, type: :form, repo: Drops.TestRepo

            schema(Test.Ecto.UserSchema)

            @impl true
            def perform(_params) do
              {:error, "Something went wrong"}
            end
          end

          {:error, failure} =
            CreateUserFormFailure.execute(%{
              "name" => "Jane Doe",
              "email" => "<EMAIL>"
            })

          # Test that Failure struct can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)
          assert form.data == %{name: "Jane Doe", email: "<EMAIL>"}

          # Test input_value function
          assert Phoenix.HTML.FormData.input_value(failure, form, :name) == "Jane Doe"

          assert Phoenix.HTML.FormData.input_value(failure, form, :email) ==
                   "<EMAIL>"

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end

    @tag ecto_schemas: [Test.Ecto.UserSchema]
    test "Failure struct with Ecto.Changeset implements Phoenix.HTML.FormData protocol" do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          defmodule CreateUserFormFailureWithChangeset do
            use TestApp, type: :form, repo: Drops.TestRepo

            schema(Test.Ecto.UserSchema)

            @impl true
            def perform(params) do
              # Create an invalid changeset to test error handling
              changeset =
                params
                |> changeset()
                |> Ecto.Changeset.add_error(:name, "is required")

              {:error, changeset}
            end
          end

          {:error, failure} =
            CreateUserFormFailureWithChangeset.execute(%{
              "name" => "",
              "email" => "<EMAIL>"
            })

          # Test that Failure struct with changeset can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)

          # The form should use the changeset data
          assert %Ecto.Changeset{} = form.source

          # Test that errors are preserved
          assert form.errors[:name] == {"is required", []}

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end
end
